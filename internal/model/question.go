package model

import (
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"time"

	"gorm.io/gorm"
)

// Question 题目表
type Question struct {
	ID           uint           `gorm:"primaryKey" json:"id"`
	Hash         string         `gorm:"uniqueIndex;size:64;not null;comment:'题目哈希值'" json:"hash"`
	Content      string         `gorm:"type:text;not null;comment:'题目内容'" json:"content"`
	QuestionType string         `gorm:"size:20;comment:'题目类型'" json:"question_type"`
	QuestionText string         `gorm:"type:text;comment:'结构化题目内容'" json:"question_text"`
	Options      string         `gorm:"type:text;comment:'选项JSON格式'" json:"options"`
	Analysis     string         `gorm:"type:text;comment:'题目解析'" json:"analysis"`
	Answer       string         `gorm:"type:text;comment:'题目答案'" json:"answer"`
	Subject      string         `gorm:"size:20;comment:'学科'" json:"subject"`
	Grade        string         `gorm:"size:20;comment:'年级'" json:"grade"`
	Difficulty   int            `gorm:"comment:'难度 1-5'" json:"difficulty"`
	SourceModel  string         `gorm:"size:50;comment:'来源模型'" json:"source_model"`
	RawContent   string         `gorm:"type:text;comment:'原始识别内容'" json:"raw_content"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `gorm:"index" json:"-"`
}

// TableName 指定表名
func (Question) TableName() string {
	return "questions"
}

// QuestionDifficulty 题目难度常量
const (
	QuestionDifficultyVeryEasy = 1 // 非常简单
	QuestionDifficultyEasy     = 2 // 简单
	QuestionDifficultyMedium   = 3 // 中等
	QuestionDifficultyHard     = 4 // 困难
	QuestionDifficultyVeryHard = 5 // 非常困难
)

// GenerateHash 生成题目哈希值
func (q *Question) GenerateHash() {
	// 使用题目内容生成MD5哈希
	content := q.Content
	if q.QuestionText != "" {
		content = q.QuestionText
	}
	hasher := md5.New()
	hasher.Write([]byte(content))
	q.Hash = hex.EncodeToString(hasher.Sum(nil))
}

// SetOptions 设置选项（将map转换为JSON字符串存储）
func (q *Question) SetOptions(options map[string]string) error {
	if len(options) == 0 {
		q.Options = ""
		return nil
	}
	data, err := json.Marshal(options)
	if err != nil {
		return err
	}
	q.Options = string(data)
	return nil
}

// GetOptions 获取选项（将JSON字符串转换为map）
func (q *Question) GetOptions() (map[string]string, error) {
	if q.Options == "" {
		return make(map[string]string), nil
	}
	var options map[string]string
	err := json.Unmarshal([]byte(q.Options), &options)
	if err != nil {
		return nil, err
	}
	return options, nil
}

// FromStructure 从QuestionStructure创建Question
func (q *Question) FromStructure(structure *QuestionStructure, analysis, answer string) error {
	// 设置基本字段
	q.QuestionType = structure.QuestionType
	q.QuestionText = structure.QuestionText
	q.Subject = structure.Subject
	q.Grade = structure.Grade
	q.Difficulty = structure.Difficulty
	q.RawContent = structure.RawContent
	q.Analysis = analysis
	q.Answer = answer

	// 向后兼容
	if structure.Content != "" {
		q.Content = structure.Content
	} else {
		q.Content = structure.QuestionText
	}

	// 设置选项
	if err := q.SetOptions(structure.Options); err != nil {
		return err
	}

	return nil
}

// QuestionSearchRequest 拍照搜题请求
type QuestionSearchRequest struct {
	ImageURL string `json:"image_url" binding:"required,url" example:"https://example.com/image.jpg"`
}

// QuestionSearchResponse 拍照搜题响应
type QuestionSearchResponse struct {
	ID           uint              `json:"id"`
	Content      string            `json:"content"`      // 兼容字段
	QuestionType string            `json:"question_type,omitempty"`
	QuestionText string            `json:"question_text,omitempty"`
	Options      map[string]string `json:"options,omitempty"`
	Analysis     string            `json:"analysis"`
	Answer       string            `json:"answer"`
	Subject      string            `json:"subject"`
	Grade        string            `json:"grade"`
	Difficulty   int               `json:"difficulty"`
	SourceModel  string            `json:"source_model"`
	CacheHit     bool              `json:"cache_hit"`    // 是否命中缓存
	ProcessTime  int64             `json:"process_time"` // 处理时间（毫秒）
}

// ToSearchResponse 转换为搜索响应格式
func (q *Question) ToSearchResponse(cacheHit bool, processTime int64) *QuestionSearchResponse {
	options, _ := q.GetOptions() // 忽略错误，返回空map

	return &QuestionSearchResponse{
		ID:           q.ID,
		Content:      q.Content,
		QuestionType: q.QuestionType,
		QuestionText: q.QuestionText,
		Options:      options,
		Analysis:     q.Analysis,
		Answer:       q.Answer,
		Subject:      q.Subject,
		Grade:        q.Grade,
		Difficulty:   q.Difficulty,
		SourceModel:  q.SourceModel,
		CacheHit:     cacheHit,
		ProcessTime:  processTime,
	}
}

// QuestionResponse 题目响应
type QuestionResponse struct {
	ID          uint      `json:"id"`
	Hash        string    `json:"hash"`
	Content     string    `json:"content"`
	Analysis    string    `json:"analysis"`
	Answer      string    `json:"answer"`
	Subject     string    `json:"subject"`
	Grade       string    `json:"grade"`
	Difficulty  int       `json:"difficulty"`
	SourceModel string    `json:"source_model"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// ToResponse 转换为响应格式
func (q *Question) ToResponse() *QuestionResponse {
	return &QuestionResponse{
		ID:          q.ID,
		Hash:        q.Hash,
		Content:     q.Content,
		Analysis:    q.Analysis,
		Answer:      q.Answer,
		Subject:     q.Subject,
		Grade:       q.Grade,
		Difficulty:  q.Difficulty,
		SourceModel: q.SourceModel,
		CreatedAt:   q.CreatedAt,
		UpdatedAt:   q.UpdatedAt,
	}
}

// QwenVLResponse Qwen-VL模型响应结构
type QwenVLResponse struct {
	Output struct {
		Choices []struct {
			Message struct {
				Content string `json:"content"`
			} `json:"message"`
		} `json:"choices"`
	} `json:"output"`
	Usage struct {
		InputTokens  int `json:"input_tokens"`
		OutputTokens int `json:"output_tokens"`
		TotalTokens  int `json:"total_tokens"`
	} `json:"usage"`
}

// DeepseekResponse Deepseek模型响应结构
type DeepseekResponse struct {
	Choices []struct {
		Message struct {
			Content string `json:"content"`
		} `json:"message"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// QuestionStructure 题目结构化数据
type QuestionStructure struct {
	QuestionType string            `json:"question_type,omitempty"` // 题目类型：单选题、多选题、填空题、解答题等
	QuestionText string            `json:"question_text"`           // 题目内容
	Options      map[string]string `json:"options,omitempty"`       // 选项（仅选择题有）
	Subject      string            `json:"subject,omitempty"`       // 学科
	Grade        string            `json:"grade,omitempty"`         // 年级
	Difficulty   int               `json:"difficulty,omitempty"`    // 难度1-5
	RawContent   string            `json:"raw_content,omitempty"`   // 原始识别内容

	// 向后兼容字段
	Content string `json:"content,omitempty"` // 兼容旧版本
}

// GenerateHashFromStructure 从结构化数据生成哈希
func GenerateHashFromStructure(structure *QuestionStructure) string {
	return GenerateSmartCacheKey(structure)
}

// GenerateSmartCacheKey 智能生成缓存键
func GenerateSmartCacheKey(structure *QuestionStructure) string {
	// 使用核心内容生成稳定的哈希
	content := structure.QuestionText
	if content == "" {
		content = structure.Content // 向后兼容
	}

	// 如果有选项，按键排序后拼接
	if len(structure.Options) > 0 {
		keys := make([]string, 0, len(structure.Options))
		for k := range structure.Options {
			keys = append(keys, k)
		}
		// 排序确保一致性
		for i := 0; i < len(keys); i++ {
			for j := i + 1; j < len(keys); j++ {
				if keys[i] > keys[j] {
					keys[i], keys[j] = keys[j], keys[i]
				}
			}
		}
		for _, k := range keys {
			content += k + structure.Options[k]
		}
	}

	// 使用SHA256生成更安全的哈希
	hasher := md5.New() // 暂时保持MD5兼容性
	hasher.Write([]byte(content))
	return hex.EncodeToString(hasher.Sum(nil))
}

// GetDifficultyName 获取难度名称
func GetDifficultyName(difficulty int) string {
	switch difficulty {
	case QuestionDifficultyVeryEasy:
		return "非常简单"
	case QuestionDifficultyEasy:
		return "简单"
	case QuestionDifficultyMedium:
		return "中等"
	case QuestionDifficultyHard:
		return "困难"
	case QuestionDifficultyVeryHard:
		return "非常困难"
	default:
		return "未知"
	}
}

// ValidateImageURL 验证图片URL
func ValidateImageURL(url string) error {
	if url == "" {
		return fmt.Errorf("图片URL不能为空")
	}
	
	// 这里可以添加更多的URL验证逻辑
	// 比如检查URL格式、文件扩展名等
	
	return nil
}

// CacheKey 生成缓存键
func GenerateCacheKey(hash string) string {
	return fmt.Sprintf("question:%s", hash)
}

// PhotoSearchRequest 拍照搜题请求结构（支持多种认证方式）
type PhotoSearchRequest struct {
	// 图片信息
	ImageURL string `json:"image_url" binding:"required"`

	// 认证方式1: 直接在请求体中（可选）
	AppKey    string `json:"app_key,omitempty"`
	SecretKey string `json:"secret_key,omitempty"`

	// 认证方式2: 嵌套在auth对象中（可选）
	Auth *AuthInfo `json:"auth,omitempty"`

	// 认证方式3: 签名认证（可选，高安全性）
	Timestamp int64  `json:"timestamp,omitempty"`
	Signature string `json:"signature,omitempty"`
}

// AuthInfo 认证信息结构
type AuthInfo struct {
	AppKey    string `json:"app_key"`
	SecretKey string `json:"secret_key"`
}

// GetAuthCredentials 获取认证凭据
func (r *PhotoSearchRequest) GetAuthCredentials() (string, string) {
	// 优先使用直接字段
	if r.AppKey != "" && r.SecretKey != "" {
		return r.AppKey, r.SecretKey
	}

	// 其次使用auth对象
	if r.Auth != nil && r.Auth.AppKey != "" && r.Auth.SecretKey != "" {
		return r.Auth.AppKey, r.Auth.SecretKey
	}

	return "", ""
}

// HasSignatureAuth 检查是否包含签名认证信息
func (r *PhotoSearchRequest) HasSignatureAuth() bool {
	return r.AppKey != "" && r.Timestamp > 0 && r.Signature != ""
}

// Validate 验证请求参数
func (r *PhotoSearchRequest) Validate() error {
	if r.ImageURL == "" {
		return fmt.Errorf("图片URL不能为空")
	}

	// 认证信息已经在中间件中验证，这里不需要再检查
	// 只验证图片URL格式
	return ValidateImageURL(r.ImageURL)
}
